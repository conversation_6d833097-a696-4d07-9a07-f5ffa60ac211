import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ViewStyle,
  TextStyle,
  ImageBackground,
} from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import {BackgroundData} from '../../../../redux/models/PostBackground';

interface ContentBackgroundProps {
  text: string;
  background: BackgroundData;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const ContentBackground: React.FC<ContentBackgroundProps> = ({
  text,
  background,
  style,
  textStyle,
}) => {
  if (!background) return null;

  // Xác định màu text - sử dụng TextColor nếu có, không thì dùng white mặc định
  const textColor = background.TextColor || 'white';

  switch (background.Type) {
    case 1: // Solid color
      return (
        <View
          style={[
            styles.container,
            {backgroundColor: background.Color},
            style,
          ]}>
          <Text style={[styles.text, {color: textColor}, textStyle]}>
            {text}
          </Text>
        </View>
      );

    case 2: // Gradient
      // Parse gradient colors từ string "color1, color2" thành array
      const gradientColors = background.Color.split(',').map(color =>
        color.trim(),
      );
      return (
        <LinearGradient
          colors={gradientColors}
          style={[
            {
              borderRadius: 8,
            },
          ]}>
          <View style={{...styles.container, ...(style ?? {})}}>
            <Text style={[styles.text, {color: textColor}, textStyle]}>
              {text}
            </Text>
          </View>
        </LinearGradient>
      );

    case 3: // Image background
      return (
        <ImageBackground
          source={{uri: background.Img}}
          style={[styles.container, style]}
          imageStyle={{borderRadius: 8}}
          resizeMode="cover">
          <Text style={[styles.text, {color: textColor}, textStyle]}>
            {text}
          </Text>
        </ImageBackground>
      );

    default:
      return (
        <View style={[styles.container, style]}>
          <Text style={[styles.text, textStyle]}>{text}</Text>
        </View>
      );
  }
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    minHeight: 200,
    padding: 16,
  },
  text: {
    width: '100%',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ContentBackground;
